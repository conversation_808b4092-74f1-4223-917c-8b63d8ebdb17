package com.aionemu.gameserver.services;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aionemu.commons.services.CronService;
import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.model.Race;
import com.aionemu.gameserver.model.gameobjects.Npc;
import com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.model.gameobjects.LetterType;
import com.aionemu.gameserver.model.templates.spawns.SpawnTemplate;
import com.aionemu.gameserver.services.instance.InstanceService;
import com.aionemu.gameserver.services.mail.SystemMailService;
import com.aionemu.gameserver.services.teleport.TeleportService;
import com.aionemu.gameserver.spawnengine.SpawnEngine;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.ThreadPoolManager;
import com.aionemu.commons.utils.Rnd;
import com.aionemu.gameserver.world.World;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * Mixfight Event Service - Custom PvP Event System
 * 
 * Features:
 * - Scheduled PvP events where all participants can attack each other
 * - Portal system for entry/exit
 * - Points-based leaderboard system
 * - Reward distribution based on performance
 * 
 * <AUTHOR>
 */
public class MixfightService {

    private static final Logger log = LoggerFactory.getLogger(MixfightService.class);

    // Event state management
    private boolean eventActive = false;
    private boolean portalsActive = false;
    private WorldMapInstance currentEventInstance;
    private int currentEventMapId;
    
    // Portal NPCs
    private Npc sanctumPortalNpc;
    private Npc pandaemoniumPortalNpc;
    
    // Participant tracking
    private final Map<Integer, MixfightParticipant> participants = new ConcurrentHashMap<>();
    private final Set<Integer> eliminatedPlayers = new HashSet<>(); // Track eliminated players
    private final Set<Integer> eventMaps = new HashSet<>();
    
    // Scheduled tasks
    private ScheduledFuture<?> eventTask;
    private ScheduledFuture<?> portalTask;
    private final List<ScheduledFuture<?>> announcementTasks = new ArrayList<>();
    
    // Singleton pattern
    private static class SingletonHolder {
        protected static final MixfightService instance = new MixfightService();
    }
    
    public static MixfightService getInstance() {
        return SingletonHolder.instance;
    }
    
    private MixfightService() {
        // Private constructor for singleton
    }
    
    /**
     * Initialize the Mixfight service
     */
    public void init() {
        if (!CustomConfig.MIXFIGHT_ENABLED) {
            log.info("Mixfight Event system is disabled.");
            return;
        }

        log.info("Initializing Mixfight Event service...");

        // Debug: Log configuration values
        log.info("DEBUG: Mixfight configuration values:");
        log.info("DEBUG: MIXFIGHT_PORTAL_SANCTUM_NPC = {}", CustomConfig.MIXFIGHT_PORTAL_SANCTUM_NPC);
        log.info("DEBUG: MIXFIGHT_PORTAL_PANDAEMONIUM_NPC = {}", CustomConfig.MIXFIGHT_PORTAL_PANDAEMONIUM_NPC);
        log.info("DEBUG: MIXFIGHT_PORTAL_SANCTUM_LOCATION = {}", CustomConfig.MIXFIGHT_PORTAL_SANCTUM_LOCATION);
        log.info("DEBUG: MIXFIGHT_PORTAL_PANDAEMONIUM_LOCATION = {}", CustomConfig.MIXFIGHT_PORTAL_PANDAEMONIUM_LOCATION);

        // Parse available maps
        parseEventMaps();

        // Schedule events using cron expression
        scheduleEvents();

        log.info("Mixfight Event service initialized successfully!");
    }
    
    /**
     * Parse event maps from configuration
     */
    private void parseEventMaps() {
        String[] mapIds = CustomConfig.MIXFIGHT_MAPS.split(",");
        for (String mapIdStr : mapIds) {
            try {
                int mapId = Integer.parseInt(mapIdStr.trim());
                eventMaps.add(mapId);
                log.debug("Added Mixfight event map: {}", mapId);
            } catch (NumberFormatException e) {
                log.warn("Invalid map ID in Mixfight configuration: {}", mapIdStr);
            }
        }
        
        if (eventMaps.isEmpty()) {
            log.warn("No valid event maps configured for Mixfight!");
        }
    }
    
    /**
     * Schedule events using cron expression
     */
    private void scheduleEvents() {
        try {
            CronService.getInstance().schedule(this::startEventSequence, CustomConfig.MIXFIGHT_SCHEDULE);
            log.info("Mixfight events scheduled with cron: {}", CustomConfig.MIXFIGHT_SCHEDULE);
        } catch (Exception e) {
            log.error("Failed to schedule Mixfight events", e);
        }
    }
    
    /**
     * Start the event sequence (portals -> event -> cleanup)
     */
    public void startEventSequence() {
        if (eventActive || portalsActive) {
            log.warn("Mixfight event already active, skipping new event");
            return;
        }
        
        log.info("Starting Mixfight event sequence");
        
        // Schedule announcements
        scheduleAnnouncements();
        
        // Start portals
        startPortals();
        
        // Schedule event start
        portalTask = ThreadPoolManager.getInstance().schedule(() -> {
            startEvent();
        }, CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES * 60 * 1000);
    }
    
    /**
     * Schedule event announcements
     */
    private void scheduleAnnouncements() {
        if (!CustomConfig.MIXFIGHT_ANNOUNCEMENTS) {
            return;
        }
        
        String[] intervals = CustomConfig.MIXFIGHT_ANNOUNCEMENT_INTERVALS.split(",");
        for (String intervalStr : intervals) {
            try {
                int minutes = Integer.parseInt(intervalStr.trim());
                long delay = (CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES - minutes) * 60 * 1000;
                
                if (delay > 0) {
                    ScheduledFuture<?> task = ThreadPoolManager.getInstance().schedule(() -> {
                        announceEvent(minutes);
                    }, delay);
                    announcementTasks.add(task);
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid announcement interval: {}", intervalStr);
            }
        }
    }
    
    /**
     * Announce upcoming event
     */
    private void announceEvent(int minutesRemaining) {
        String message = String.format(
            "Mixfight Event starts in %d minute%s! Portals are now open in Sanctum and Pandaemonium. Level %d+ players can participate!",
            minutesRemaining, minutesRemaining == 1 ? "" : "s", CustomConfig.MIXFIGHT_MIN_LEVEL
        );
        
        World.getInstance().forEachPlayer(player -> {
            PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
        });
        
        log.info("Announced Mixfight event: {} minutes remaining", minutesRemaining);
    }
    
    /**
     * Start portals in Sanctum and Pandaemonium
     */
    private void startPortals() {
        portalsActive = true;
        portalStartTime = System.currentTimeMillis();

        log.info("DEBUG: Starting Mixfight portals - clearing any existing portals first");

        // Ensure clean portal spawning by clearing any existing portals
        clearExistingPortals();

        // Spawn Sanctum portal
        spawnSanctumPortal();

        // Spawn Pandaemonium portal
        spawnPandaemoniumPortal();

        // Announce portal opening
        if (CustomConfig.MIXFIGHT_ANNOUNCEMENTS) {
            String message = String.format(
                "Mixfight Event portals are now open! Event starts in %d minutes. Level %d+ players can enter!",
                CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES, CustomConfig.MIXFIGHT_MIN_LEVEL
            );

            World.getInstance().forEachPlayer(player -> {
                PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
            });
        }

        log.info("Mixfight portals activated");
    }

    /**
     * Clear any existing portals that might interfere with Mixfight portal spawning
     */
    private void clearExistingPortals() {
        try {
            // Clear our own portals first
            if (sanctumPortalNpc != null) {
                log.info("DEBUG: Clearing existing Sanctum Mixfight portal");
                sanctumPortalNpc.getController().delete();
                sanctumPortalNpc = null;
            }

            if (pandaemoniumPortalNpc != null) {
                log.info("DEBUG: Clearing existing Pandaemonium Mixfight portal");
                pandaemoniumPortalNpc.getController().delete();
                pandaemoniumPortalNpc = null;
            }

            log.info("DEBUG: Cleared existing Mixfight portals");
        } catch (Exception e) {
            log.error("Error clearing existing portals", e);
        }
    }


    
    /**
     * Spawn portal NPC in Sanctum
     */
    private void spawnSanctumPortal() {
        try {
            // Clear any existing portal first
            if (sanctumPortalNpc != null && sanctumPortalNpc.isSpawned()) {
                log.info("DEBUG: Removing existing Sanctum portal before spawning new one");
                sanctumPortalNpc.getController().delete();
                sanctumPortalNpc = null;
            }

            String[] coords = CustomConfig.MIXFIGHT_PORTAL_SANCTUM_LOCATION.split(",");
            float x = Float.parseFloat(coords[0]);
            float y = Float.parseFloat(coords[1]);
            float z = Float.parseFloat(coords[2]);
            byte h = Byte.parseByte(coords[3]);

            log.info("DEBUG: Spawning Sanctum Mixfight portal with NPC ID: {} at coordinates {}, {}, {}",
                CustomConfig.MIXFIGHT_PORTAL_SANCTUM_NPC, x, y, z);

            SpawnTemplate spawn = SpawnEngine.newSingleTimeSpawn(110010000, CustomConfig.MIXFIGHT_PORTAL_SANCTUM_NPC,
                x, y, z, h, 0, "mixfight_portal");
            sanctumPortalNpc = (Npc) SpawnEngine.spawnObject(spawn, 0);

            if (sanctumPortalNpc != null) {
                log.info("DEBUG: Successfully spawned Sanctum Mixfight portal - NPC ID: {}, Object ID: {}, Name: {}",
                    sanctumPortalNpc.getNpcId(), sanctumPortalNpc.getObjectId(), sanctumPortalNpc.getName());
            } else {
                log.error("DEBUG: Failed to spawn Sanctum Mixfight portal - SpawnEngine returned null");
            }
        } catch (Exception e) {
            log.error("Failed to spawn Sanctum Mixfight portal", e);
        }
    }
    
    /**
     * Spawn portal NPC in Pandaemonium
     */
    private void spawnPandaemoniumPortal() {
        try {
            // Clear any existing portal first
            if (pandaemoniumPortalNpc != null && pandaemoniumPortalNpc.isSpawned()) {
                log.info("DEBUG: Removing existing Pandaemonium portal before spawning new one");
                pandaemoniumPortalNpc.getController().delete();
                pandaemoniumPortalNpc = null;
            }

            String[] coords = CustomConfig.MIXFIGHT_PORTAL_PANDAEMONIUM_LOCATION.split(",");
            float x = Float.parseFloat(coords[0]);
            float y = Float.parseFloat(coords[1]);
            float z = Float.parseFloat(coords[2]);
            byte h = Byte.parseByte(coords[3]);

            log.info("DEBUG: Spawning Pandaemonium Mixfight portal with NPC ID: {} at coordinates {}, {}, {}",
                CustomConfig.MIXFIGHT_PORTAL_PANDAEMONIUM_NPC, x, y, z);

            SpawnTemplate spawn = SpawnEngine.newSingleTimeSpawn(120010000, CustomConfig.MIXFIGHT_PORTAL_PANDAEMONIUM_NPC,
                x, y, z, h, 0, "mixfight_portal");
            pandaemoniumPortalNpc = (Npc) SpawnEngine.spawnObject(spawn, 0);

            if (pandaemoniumPortalNpc != null) {
                log.info("DEBUG: Successfully spawned Pandaemonium Mixfight portal - NPC ID: {}, Object ID: {}, Name: {}",
                    pandaemoniumPortalNpc.getNpcId(), pandaemoniumPortalNpc.getObjectId(), pandaemoniumPortalNpc.getName());
            } else {
                log.error("DEBUG: Failed to spawn Pandaemonium Mixfight portal - SpawnEngine returned null");
            }
        } catch (Exception e) {
            log.error("Failed to spawn Pandaemonium Mixfight portal", e);
        }
    }
    
    /**
     * Start the actual Mixfight event
     */
    private void startEvent() {
        if (eventActive) {
            log.warn("Mixfight event already active");
            return;
        }
        
        // Select random map
        currentEventMapId = selectRandomEventMap();
        if (currentEventMapId == 0) {
            log.error("No valid event map available, canceling event");
            cleanup();
            return;
        }
        
        // Create instance
        currentEventInstance = InstanceService.getNextAvailableInstance(currentEventMapId, 0, (byte) 0,
            CustomConfig.MIXFIGHT_MAX_PLAYERS, true);

        eventActive = true;
        eventStartTime = System.currentTimeMillis();
        
        // Close portals
        closePortals();
        
        // Announce event start
        announceEventStart();
        
        // Schedule event end
        eventTask = ThreadPoolManager.getInstance().schedule(() -> {
            endEvent();
        }, CustomConfig.MIXFIGHT_DURATION_MINUTES * 60 * 1000);
        
        log.info("Mixfight event started on map {} with instance {}", currentEventMapId, 
            currentEventInstance.getInstanceId());
    }
    
    /**
     * Select a random event map
     */
    private int selectRandomEventMap() {
        if (eventMaps.isEmpty()) {
            return 0;
        }
        
        List<Integer> mapList = new ArrayList<>(eventMaps);
        return mapList.get(new Random().nextInt(mapList.size()));
    }
    
    /**
     * Close portals when event starts
     */
    private void closePortals() {
        portalsActive = false;
        
        if (sanctumPortalNpc != null) {
            sanctumPortalNpc.getController().delete();
            sanctumPortalNpc = null;
        }
        
        if (pandaemoniumPortalNpc != null) {
            pandaemoniumPortalNpc.getController().delete();
            pandaemoniumPortalNpc = null;
        }
        
        log.debug("Mixfight portals closed");
    }
    
    /**
     * Announce event start
     */
    private void announceEventStart() {
        String message = String.format(
            "Mixfight Event has begun! Duration: %d minutes. Fight for glory and rewards!",
            CustomConfig.MIXFIGHT_DURATION_MINUTES
        );
        
        // Announce to all participants
        for (MixfightParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null) {
                PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
            }
        }
        
        log.info("Announced Mixfight event start to {} participants", participants.size());
    }
    
    /**
     * End the Mixfight event
     */
    private void endEvent() {
        if (!eventActive) {
            return;
        }

        log.info("Ending Mixfight event");

        // Calculate and distribute rewards
        distributeRewards();

        // Remove all participants from event
        removeAllParticipants();

        // Cleanup
        cleanup();

        // Announce event end
        announceEventEnd();
    }

    /**
     * Distribute rewards to participants based on their final ranking
     */
    private void distributeRewards() {
        if (participants.isEmpty()) {
            log.info("No participants to reward");
            return;
        }

        log.info("Distributing rank-based rewards to {} participants", participants.size());

        // Get final leaderboard (sorted by AP)
        List<MixfightParticipant> finalLeaderboard = getLeaderboard();

        for (int i = 0; i < finalLeaderboard.size(); i++) {
            MixfightParticipant participant = finalLeaderboard.get(i);
            int rank = i + 1; // 1-based ranking
            String playerName = participant.getPlayerName();

            // Determine reward tier based on rank
            RewardTier tier = determineRewardTierByRank(rank, finalLeaderboard.size());

            // Send rewards via mail
            sendRewards(playerName, tier, participant, rank);

            log.debug("Rewarded player {} (Rank: {}, AP: {}, Kills: {}) with tier {}",
                playerName, rank, participant.getAccumulatedAP(), participant.getKills(), tier.name());
        }
    }

    /**
     * Determine reward tier based on final rank position
     */
    private RewardTier determineRewardTierByRank(int rank, int totalParticipants) {
        // Rank-based reward system:
        // Top 10% get TIER1 (best rewards)
        // Next 20% get TIER2
        // Next 30% get TIER3
        // Next 40% get TIER4
        // Everyone else gets PARTICIPATION

        if (rank == 1) {
            // Winner always gets TIER1
            return RewardTier.TIER1;
        }

        double percentile = (double) rank / totalParticipants;

        if (percentile <= 0.10) { // Top 10%
            return RewardTier.TIER1;
        } else if (percentile <= 0.30) { // Top 30% (next 20%)
            return RewardTier.TIER2;
        } else if (percentile <= 0.60) { // Top 60% (next 30%)
            return RewardTier.TIER3;
        } else if (percentile <= 1.00) { // Top 100% (remaining 40%)
            return RewardTier.TIER4;
        } else {
            return RewardTier.PARTICIPATION;
        }
    }

    /**
     * Determine reward tier based on accumulated AP (legacy method - kept for compatibility)
     */
    @SuppressWarnings("unused")
    private RewardTier determineRewardTier(int accumulatedAP) {
        if (accumulatedAP >= parseRewardConfig(CustomConfig.MIXFIGHT_REWARDS_TIER1)[0]) {
            return RewardTier.TIER1;
        } else if (accumulatedAP >= parseRewardConfig(CustomConfig.MIXFIGHT_REWARDS_TIER2)[0]) {
            return RewardTier.TIER2;
        } else if (accumulatedAP >= parseRewardConfig(CustomConfig.MIXFIGHT_REWARDS_TIER3)[0]) {
            return RewardTier.TIER3;
        } else if (accumulatedAP >= parseRewardConfig(CustomConfig.MIXFIGHT_REWARDS_TIER4)[0]) {
            return RewardTier.TIER4;
        } else {
            return RewardTier.PARTICIPATION;
        }
    }

    /**
     * Parse reward configuration string
     * Format: minAP:itemId:itemCount:kinah:honorPoints (for tiers)
     * Format: itemId:itemCount:kinah:honorPoints (for participation)
     */
    private int[] parseRewardConfig(String config) {
        String[] parts = config.split(":");
        int[] values = new int[parts.length];
        for (int i = 0; i < parts.length; i++) {
            values[i] = Integer.parseInt(parts[i]);
        }
        return values;
    }

    /**
     * Send rewards to player via mail based on their final rank
     */
    private void sendRewards(String playerName, RewardTier tier, MixfightParticipant participant, int rank) {
        int[] rewardData;
        String title = "Mixfight Event Rewards";
        String message;

        if (tier == RewardTier.PARTICIPATION) {
            rewardData = parseRewardConfig(CustomConfig.MIXFIGHT_REWARDS_PARTICIPATION);
            message = String.format("Thank you for participating in the Mixfight Event! You finished rank #%d with %d AP and %d kills.",
                rank, participant.getAccumulatedAP(), participant.getKills());

            // Send participation reward
            SystemMailService.sendMail("Mixfight System", playerName, title, message,
                rewardData[0], rewardData[1], rewardData[2], LetterType.EXPRESS);

            // Add honor points if configured
            if (rewardData.length > 3 && rewardData[3] > 0) {
                // Add honor points logic here when implemented
            }
        } else {
            // Get tier reward configuration
            String tierConfig = switch (tier) {
                case TIER1 -> CustomConfig.MIXFIGHT_REWARDS_TIER1;
                case TIER2 -> CustomConfig.MIXFIGHT_REWARDS_TIER2;
                case TIER3 -> CustomConfig.MIXFIGHT_REWARDS_TIER3;
                case TIER4 -> CustomConfig.MIXFIGHT_REWARDS_TIER4;
                default -> CustomConfig.MIXFIGHT_REWARDS_PARTICIPATION;
            };

            rewardData = parseRewardConfig(tierConfig);

            // Create rank-specific message
            String rankMessage = switch (rank) {
                case 1 -> "CHAMPION! You won the Mixfight Event!";
                case 2 -> "Excellent! You finished 2nd place!";
                case 3 -> "Great job! You finished 3rd place!";
                default -> String.format("Well done! You finished rank #%d!", rank);
            };

            message = String.format("%s You earned %d AP and achieved %d kills during the event. Tier: %s",
                rankMessage, participant.getAccumulatedAP(), participant.getKills(), tier.name());

            // Send tier reward (skip minAP value at index 0)
            SystemMailService.sendMail("Mixfight System", playerName, title, message,
                rewardData[1], rewardData[2], rewardData[3], LetterType.EXPRESS);

            // Add honor points if configured
            if (rewardData.length > 4 && rewardData[4] > 0) {
                // Add honor points logic here when implemented
            }
        }
    }

    /**
     * Remove all participants from event
     */
    private void removeAllParticipants() {
        for (int playerId : participants.keySet()) {
            Player player = World.getInstance().getPlayer(playerId);
            if (player != null) {
                removeParticipant(player);
            }
        }
        participants.clear();
    }

    /**
     * Remove participant from event
     */
    public void removeParticipant(Player player) {
        if (player == null) {
            return;
        }

        // Remove Mixfight participant state
        player.unsetCustomState(CustomPlayerState.MIXFIGHT_PARTICIPANT);

        // Also remove enemy of all players state
        player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);

        // Update player attributes
        player.getController().onChangedPlayerAttributes();

        // Teleport out of event instance if still inside
        if (currentEventInstance != null && player.getInstanceId() == currentEventInstance.getInstanceId()) {
            // Teleport to appropriate capital city
            if (player.getRace() == Race.ELYOS) {
                TeleportService.teleportTo(player, 110010000, 1313.25f, 1512.011f, 568.107f, (byte) 0);
            } else {
                TeleportService.teleportTo(player, 120010000, 1685.7f, 1400.5f, 195.48618f, (byte) 60);
            }
        }

        participants.remove(player.getObjectId());
        log.debug("Removed player {} from Mixfight event", player.getName());
    }

    /**
     * Cleanup event resources
     */
    private void cleanup() {
        eventActive = false;
        portalsActive = false;
        currentEventInstance = null;
        currentEventMapId = 0;

        // Clear eliminated players for next event
        eliminatedPlayers.clear();

        // Cancel scheduled tasks
        if (eventTask != null) {
            eventTask.cancel(false);
            eventTask = null;
        }

        if (portalTask != null) {
            portalTask.cancel(false);
            portalTask = null;
        }

        for (ScheduledFuture<?> task : announcementTasks) {
            task.cancel(false);
        }
        announcementTasks.clear();

        // Remove portal NPCs if still active
        closePortals();

        log.debug("Mixfight event cleanup completed");
    }

    /**
     * Announce event end
     */
    private void announceEventEnd() {
        String message = "Mixfight Event has ended! Check your mail for rewards.";

        World.getInstance().forEachPlayer(player -> {
            PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
        });

        log.info("Announced Mixfight event end");
    }

    // Public methods for external interaction

    /**
     * Check if player can enter Mixfight event
     */
    public boolean canEnterEvent(Player player) {
        if (!CustomConfig.MIXFIGHT_ENABLED || !portalsActive) {
            return false;
        }

        if (player.getLevel() < CustomConfig.MIXFIGHT_MIN_LEVEL) {
            return false;
        }

        if (participants.size() >= CustomConfig.MIXFIGHT_MAX_PLAYERS) {
            return false;
        }

        // Check if player is already participating
        if (participants.containsKey(player.getObjectId())) {
            return false;
        }

        // Check if player has been eliminated from current event
        if (eliminatedPlayers.contains(player.getObjectId())) {
            return false;
        }

        return true;
    }

    /**
     * Add player to Mixfight event
     */
    public boolean enterEvent(Player player) {
        if (!canEnterEvent(player)) {
            return false;
        }

        // Add participant
        MixfightParticipant participant = new MixfightParticipant(player.getObjectId(), player.getName());
        participants.put(player.getObjectId(), participant);

        // Set Mixfight participant state
        player.setCustomState(CustomPlayerState.MIXFIGHT_PARTICIPANT);

        // Also set as enemy of all players to force client-side attackability (bypass client restrictions)
        player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);

        player.getController().onChangedPlayerAttributes();

        // Remove any teleportation protection after a short delay to ensure PvP works immediately
        ThreadPoolManager.getInstance().schedule(() -> {
            if (player.isOnline() && participants.containsKey(player.getObjectId())) {
                // Remove invulnerability and teleportation protection states
                player.unsetCustomState(CustomPlayerState.INVULNERABLE);
                player.unsetCustomState(CustomPlayerState.TELEPORTATION_MODE);
                player.getController().onChangedPlayerAttributes();
            }
        }, 3000); // 3 second delay

        // Teleport to waiting area or event instance
        if (eventActive && currentEventInstance != null) {
            // Event is active, teleport to event instance (all players go to same map)
            teleportToEventMap(player, currentEventInstance.getMapId(), currentEventInstance.getInstanceId());
        } else {
            // Event not started yet, teleport to waiting area (use the same map that will be used for event)
            if (currentEventMapId != 0) {
                teleportToEventMap(player, currentEventMapId, 0);
            } else {
                // Fallback: select a map if none is set yet
                int waitingMapId = selectRandomEventMap();
                if (waitingMapId != 0) {
                    currentEventMapId = waitingMapId;
                    teleportToEventMap(player, waitingMapId, 0);
                }
            }
        }

        PacketSendUtility.sendMessage(player, "You have joined the Mixfight Event! Prepare for battle!", ChatType.BRIGHT_YELLOW_CENTER);
        log.debug("Player {} joined Mixfight event", player.getName());

        return true;
    }

    /**
     * Handle player kill in Mixfight event
     */
    public void onPlayerKill(Player killer, Player victim) {
        // Allow kills during both portal phase AND active event phase
        if (!eventActive && !portalsActive) {
            return;
        }

        MixfightParticipant killerParticipant = participants.get(killer.getObjectId());

        if (killerParticipant == null) {
            return;
        }

        // Increment kills
        killerParticipant.incrementKills();

        // Calculate AP reward with multiplier
        int baseAP = 100; // Base AP for kill
        int bonusAP = (int) (baseAP * CustomConfig.MIXFIGHT_POINTS_MULTIPLIER);
        killerParticipant.addAP(bonusAP);

        // Send message to killer
        PacketSendUtility.sendMessage(killer,
            String.format("Mixfight Kill! +%d AP (Total: %d AP, %d kills)",
                bonusAP, killerParticipant.getAccumulatedAP(), killerParticipant.getKills()),
            ChatType.BRIGHT_YELLOW_CENTER);

        // Eliminate the victim from the event
        eliminatePlayer(victim);

        log.debug("Player {} killed {} in Mixfight event (AP: {}, Kills: {})",
            killer.getName(), victim.getName(), killerParticipant.getAccumulatedAP(), killerParticipant.getKills());
    }

    /**
     * Eliminate a player from the Mixfight event
     */
    public void eliminatePlayer(Player player) {
        if (player == null || !participants.containsKey(player.getObjectId())) {
            return;
        }

        // Add to eliminated players list
        eliminatedPlayers.add(player.getObjectId());

        // Remove Mixfight participant state but keep them in participants for rewards
        player.unsetCustomState(CustomPlayerState.MIXFIGHT_PARTICIPANT);
        player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();

        // Teleport out of event instance if still inside
        if (currentEventInstance != null && player.getInstanceId() == currentEventInstance.getInstanceId()) {
            // Teleport to appropriate capital city
            if (player.getRace() == Race.ELYOS) {
                TeleportService.teleportTo(player, 110010000, 1313.25f, 1512.011f, 568.107f, (byte) 0);
            } else {
                TeleportService.teleportTo(player, 120010000, 1685.7f, 1400.5f, 195.48618f, (byte) 60);
            }
        }

        // DON'T remove from participants map - they need to stay for rewards!
        // participants.remove(player.getObjectId()); // REMOVED THIS LINE

        // Send elimination message
        PacketSendUtility.sendMessage(player,
            "You have been eliminated from the Mixfight Event! You cannot re-enter, but you will still receive rewards based on your performance.",
            ChatType.BRIGHT_YELLOW_CENTER);

        log.info("Player {} has been eliminated from Mixfight event but will still receive rewards", player.getName());
    }

    /**
     * Check if event is currently active
     */
    public boolean isEventActive() {
        return eventActive;
    }

    /**
     * Check if portals are currently active
     */
    public boolean arePortalsActive() {
        return portalsActive;
    }

    /**
     * Get current participant count
     */
    public int getParticipantCount() {
        return participants.size();
    }

    /**
     * Get participant data for a player
     */
    public MixfightParticipant getParticipant(int playerId) {
        return participants.get(playerId);
    }

    /**
     * Force end event (admin command)
     */
    public void forceEndEvent() {
        if (eventActive || portalsActive) {
            log.info("Force ending Mixfight event");

            if (eventActive) {
                // Event is running, end it normally with rewards
                endEvent();
            } else if (portalsActive) {
                // Portal phase is active, end with rewards too
                log.info("Force ending portal phase with rewards");

                // Distribute rewards to participants
                distributeRewards();

                // Remove all participants from portal phase
                removeAllParticipants();

                // Cleanup everything
                cleanup();

                // Announce event end with rewards
                announceEventEnd();

                log.info("Mixfight portal phase ended with rewards distributed");
            }
        }
    }

    /**
     * Force start event (admin command)
     */
    public void forceStartEvent() {
        if (!eventActive && !portalsActive) {
            log.info("Force starting Mixfight event");
            startEventSequence();
        }
    }

    /**
     * Get current leaderboard sorted by accumulated AP
     */
    public List<MixfightParticipant> getLeaderboard() {
        return participants.values().stream()
            .sorted((p1, p2) -> Integer.compare(p2.getAccumulatedAP(), p1.getAccumulatedAP()))
            .collect(Collectors.toList());
    }

    /**
     * Get top N participants from leaderboard
     */
    public List<MixfightParticipant> getTopParticipants(int count) {
        return getLeaderboard().stream()
            .limit(count)
            .collect(Collectors.toList());
    }

    /**
     * Get participant rank (1-based)
     */
    public int getParticipantRank(int playerId) {
        List<MixfightParticipant> leaderboard = getLeaderboard();
        for (int i = 0; i < leaderboard.size(); i++) {
            if (leaderboard.get(i).getPlayerId() == playerId) {
                return i + 1; // 1-based rank
            }
        }
        return -1; // Not found
    }

    // Admin command support methods

    /**
     * Check if system is enabled
     */
    public boolean isEnabled() {
        return CustomConfig.MIXFIGHT_ENABLED;
    }



    /**
     * Get max players
     */
    public int getMaxPlayers() {
        return CustomConfig.MIXFIGHT_MAX_PLAYERS;
    }

    /**
     * Get current event map ID
     */
    public int getCurrentEventMapId() {
        return currentEventMapId;
    }

    /**
     * Get time remaining in minutes
     */
    public int getTimeRemaining() {
        if (!eventActive || eventTask == null) {
            return 0;
        }
        // Calculate remaining time based on event duration
        return Math.max(0, CustomConfig.MIXFIGHT_DURATION_MINUTES -
            (int)((System.currentTimeMillis() - eventStartTime) / 60000));
    }

    /**
     * Get portal time remaining in minutes
     */
    public int getPortalTimeRemaining() {
        if (!portalsActive || portalTask == null) {
            return 0;
        }
        // Calculate remaining time based on portal duration
        return Math.max(0, CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES -
            (int)((System.currentTimeMillis() - portalStartTime) / 60000));
    }

    /**
     * Get next event time as string
     */
    public String getNextEventTime() {
        if (eventActive || portalsActive) {
            return "Event currently running";
        }
        return "Check cron schedule: " + CustomConfig.MIXFIGHT_SCHEDULE;
    }

    /**
     * Get leaderboard as formatted string
     */
    public String getLeaderboardString(int count) {
        List<MixfightParticipant> leaderboard = getLeaderboard();
        if (leaderboard.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("=== Mixfight Leaderboard ===\n");
        int displayCount = Math.min(count, leaderboard.size());

        for (int i = 0; i < displayCount; i++) {
            MixfightParticipant p = leaderboard.get(i);
            sb.append(String.format("%d. %s - %d AP (%d kills)\n",
                i + 1, p.getPlayerName(), p.getAccumulatedAP(), p.getKills()));
        }

        return sb.toString();
    }

    /**
     * Get player stats as formatted string
     */
    public String getPlayerStatsString(String playerName) {
        for (MixfightParticipant p : participants.values()) {
            if (p.getPlayerName().equalsIgnoreCase(playerName)) {
                StringBuilder sb = new StringBuilder();
                sb.append("=== ").append(p.getPlayerName()).append(" Stats ===\n");
                sb.append("Accumulated AP: ").append(p.getAccumulatedAP()).append("\n");
                sb.append("Kills: ").append(p.getKills()).append("\n");
                sb.append("Rank: ").append(getParticipantRank(p.getPlayerId())).append("\n");
                sb.append("Join Time: ").append(new java.util.Date(p.getJoinTime())).append("\n");
                return sb.toString();
            }
        }
        return "";
    }

    /**
     * Get configuration values
     */
    public String getSchedule() {
        return CustomConfig.MIXFIGHT_SCHEDULE;
    }

    public int getDurationMinutes() {
        return CustomConfig.MIXFIGHT_DURATION_MINUTES;
    }

    public int getPortalDurationMinutes() {
        return CustomConfig.MIXFIGHT_PORTAL_DURATION_MINUTES;
    }

    public int getMinLevel() {
        return CustomConfig.MIXFIGHT_MIN_LEVEL;
    }

    public float getPointsMultiplier() {
        return CustomConfig.MIXFIGHT_POINTS_MULTIPLIER;
    }

    public String getAvailableMaps() {
        return CustomConfig.MIXFIGHT_MAPS;
    }

    public boolean areAnnouncementsEnabled() {
        return CustomConfig.MIXFIGHT_ANNOUNCEMENTS;
    }

    /**
     * Get participants as formatted string
     */
    public String getParticipantsString() {
        if (participants.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("=== Current Participants (").append(participants.size()).append(") ===\n");

        for (MixfightParticipant p : participants.values()) {
            sb.append("- ").append(p.getPlayerName())
              .append(" (AP: ").append(p.getAccumulatedAP())
              .append(", Kills: ").append(p.getKills()).append(")\n");
        }

        return sb.toString();
    }

    // Add missing time tracking fields
    private long eventStartTime = 0;
    private long portalStartTime = 0;

    /**
     * Send leaderboard update to all participants
     */
    public void broadcastLeaderboard() {
        if (!eventActive) {
            return;
        }

        List<MixfightParticipant> top10 = getTopParticipants(10);
        StringBuilder message = new StringBuilder("Mixfight Leaderboard (Top 10):\n");

        for (int i = 0; i < top10.size(); i++) {
            MixfightParticipant participant = top10.get(i);
            String playerName = participant.getPlayerName();

            message.append(String.format("%d. %s - %d AP (%d kills)\n",
                i + 1, playerName, participant.getAccumulatedAP(), participant.getKills()));
        }

        // Send to all participants
        for (MixfightParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null) {
                PacketSendUtility.sendMessage(player, message.toString(), ChatType.BRIGHT_YELLOW_CENTER);
            }
        }
    }

    /**
     * Send personal stats to a player
     */
    public void sendPersonalStats(Player player) {
        MixfightParticipant participant = participants.get(player.getObjectId());
        if (participant == null) {
            PacketSendUtility.sendMessage(player, "You are not participating in the Mixfight event.", ChatType.BRIGHT_YELLOW_CENTER);
            return;
        }

        int rank = getParticipantRank(player.getObjectId());
        String message = String.format(
            "Your Mixfight Stats:\nRank: %d\nAP Earned: %d\nKills: %d\nTime in Event: %d minutes",
            rank, participant.getAccumulatedAP(), participant.getKills(),
            (System.currentTimeMillis() - participant.getJoinTime()) / 60000
        );

        PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
    }

    // Reward tier enumeration
    private enum RewardTier {
        TIER1, TIER2, TIER3, TIER4, PARTICIPATION
    }

    // Cache for parsed spawn coordinates
    private final Map<Integer, List<float[]>> spawnCoordinatesCache = new HashMap<>();

    /**
     * Get random spawn coordinates for the specified map
     * Returns [x, y, z, heading]
     */
    private float[] getRandomSpawnCoordinates(int mapId) {
        // Check cache first
        List<float[]> coords = spawnCoordinatesCache.get(mapId);
        if (coords == null) {
            log.info("DEBUG: Loading spawn coordinates for map {} from configuration", mapId);
            coords = parseSpawnCoordinatesForMap(mapId);
            spawnCoordinatesCache.put(mapId, coords);
            log.info("DEBUG: Cached {} coordinates for map {}", coords.size(), mapId);
        }

        if (coords.isEmpty()) {
            log.warn("No spawn coordinates found for map {}, using fallback coordinates", mapId);
            return new float[]{242.52405f, 424.71637f, 103.80612f, 0};
        }

        float[] selectedCoords = Rnd.get(coords);
        log.info("DEBUG: Selected random coordinates for map {}: x={}, y={}, z={}, h={}",
            mapId, selectedCoords[0], selectedCoords[1], selectedCoords[2], selectedCoords[3]);
        return selectedCoords;
    }

    /**
     * Parse spawn coordinates for a specific map from configuration
     */
    private List<float[]> parseSpawnCoordinatesForMap(int mapId) {
        List<float[]> coords = new ArrayList<>();

        // Get the configuration property for this map
        String configKey = "gameserver.mixfight.spawn." + mapId;
        String coordsString = getConfigProperty(configKey);

        if (coordsString == null || coordsString.trim().isEmpty()) {
            log.warn("No spawn coordinates configured for map {} (property: {})", mapId, configKey);
            return coords;
        }

        // Parse coordinates: format is "x1,y1,z1,h1;x2,y2,z2,h2;..."
        String[] coordGroups = coordsString.split(";");
        for (String coordGroup : coordGroups) {
            coordGroup = coordGroup.trim();
            if (coordGroup.isEmpty()) continue;

            try {
                String[] parts = coordGroup.split(",");
                if (parts.length >= 3) {
                    float x = Float.parseFloat(parts[0].trim());
                    float y = Float.parseFloat(parts[1].trim());
                    float z = Float.parseFloat(parts[2].trim());
                    float h = parts.length > 3 ? Float.parseFloat(parts[3].trim()) : 0;

                    coords.add(new float[]{x, y, z, h});
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid coordinate format in {}: {}", configKey, coordGroup);
            }
        }

        log.info("Loaded {} spawn coordinates for map {} from configuration", coords.size(), mapId);
        return coords;
    }

    /**
     * Get configuration property with fallback to hardcoded values
     */
    private String getConfigProperty(String key) {
        // Get from configuration properties
        switch (key) {
            case "gameserver.mixfight.spawn.300030000":
                return CustomConfig.MIXFIGHT_SPAWN_300030000;
            case "gameserver.mixfight.spawn.300040000":
                return CustomConfig.MIXFIGHT_SPAWN_300040000;
            case "gameserver.mixfight.spawn.300230000":
                return CustomConfig.MIXFIGHT_SPAWN_300230000;
            default:
                return null;
        }
    }

    /**
     * Teleport player to appropriate coordinates for the given map using random spawn locations
     */
    private void teleportToEventMap(Player player, int mapId, int instanceId) {
        try {
            // Get random spawn coordinates based on the current map
            float[] coords = getRandomSpawnCoordinates(mapId);

            if (instanceId > 0) {
                // Teleport to instance
                TeleportService.teleportTo(player, mapId, instanceId, coords[0], coords[1], coords[2], (byte) coords[3]);
            } else {
                // Teleport to regular map
                TeleportService.teleportTo(player, mapId, coords[0], coords[1], coords[2], (byte) coords[3]);
            }

            log.debug("Teleported player {} to Mixfight map {} at random coordinates ({}, {}, {})",
                player.getName(), mapId, coords[0], coords[1], coords[2]);
        } catch (Exception e) {
            log.error("Failed to teleport player {} to Mixfight event map", player.getName(), e);
        }
    }

    /**
     * Clear spawn coordinates cache (useful for configuration reloads)
     */
    public void clearSpawnCoordinatesCache() {
        spawnCoordinatesCache.clear();
        log.info("Mixfight spawn coordinates cache cleared");
    }

    // Debug methods for admin commands
    public Npc getSanctumPortalNpc() {
        return sanctumPortalNpc;
    }

    public Npc getPandaemoniumPortalNpc() {
        return pandaemoniumPortalNpc;
    }

    // Inner class for participant data
    public static class MixfightParticipant {
        private final int playerId;
        private final String playerName;
        private int accumulatedAP = 0;
        private int kills = 0;
        private long joinTime;

        public MixfightParticipant(int playerId, String playerName) {
            this.playerId = playerId;
            this.playerName = playerName;
            this.joinTime = System.currentTimeMillis();
        }

        // Getters and setters
        public int getPlayerId() { return playerId; }
        public String getPlayerName() { return playerName; }
        public int getAccumulatedAP() { return accumulatedAP; }
        public void addAP(int ap) { this.accumulatedAP += ap; }
        public int getKills() { return kills; }
        public void incrementKills() { this.kills++; }
        public long getJoinTime() { return joinTime; }
    }
}
