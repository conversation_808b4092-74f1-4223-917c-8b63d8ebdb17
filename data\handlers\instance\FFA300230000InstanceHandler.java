package instance;

import com.aionemu.gameserver.instance.handlers.InstanceID;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * FFA Instance Handler for Kromede's Trial (300230000)
 * 
 * <AUTHOR> System
 */
@InstanceID(300230000)
public class FFA300230000InstanceHandler extends FFAInstanceHandler {

    public FFA300230000InstanceHandler(WorldMapInstance instance) {
        super(instance);
    }

    @Override
    protected void initializeSpawnLocations() {
        addSpawnLocation(613.59827f, 583.16455f, 208.99329f, (byte) 54);
        addSpawnLocation(580.4837f, 614.0316f, 208.98824f, (byte) 10);
        addSpawnLocation(581.04785f, 666.2814f, 208.988f, (byte) 108);
        addSpawnLocation(572.64386f, 625.2187f, 206.96425f, (byte) 24);
        addSpawnLocation(521.0341f, 612.5965f, 201.62512f, (byte) 25);
        addSpawnLocation(484.47202f, 618.8112f, 201.58958f, (byte) 32);
        addSpawnLocation(493.47336f, 669.7233f, 201.55687f, (byte) 106);
    }
}
