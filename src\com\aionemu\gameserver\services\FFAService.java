package com.aionemu.gameserver.services;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.Map;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aionemu.commons.services.CronService;
import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState;
import com.aionemu.gameserver.services.teleport.TeleportService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.ThreadPoolManager;
import com.aionemu.gameserver.world.World;
import com.aionemu.gameserver.world.WorldMapInstance;
import com.aionemu.gameserver.services.instance.InstanceService;
import com.aionemu.gameserver.model.gameobjects.Npc;
import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.services.mail.SystemMailService;
import com.aionemu.gameserver.model.gameobjects.LetterType;
import com.aionemu.commons.utils.Rnd;
import java.util.ArrayList;
import java.util.List;
import com.aionemu.gameserver.dataholders.DataManager;
import com.aionemu.gameserver.model.templates.item.ItemTemplate;
import com.aionemu.gameserver.model.gameobjects.Item;
import com.aionemu.gameserver.network.aion.serverpackets.SM_UPDATE_PLAYER_APPEARANCE;
import com.aionemu.gameserver.network.aion.serverpackets.SM_CUSTOM_SETTINGS;
import com.aionemu.gameserver.network.aion.serverpackets.SM_RENAME;

import com.aionemu.gameserver.model.Gender;


/**
 * FFA (Free-For-All) Event Service
 * 
 * Features:
 * - All players teleport to a single map where everyone can attack each other
 * - Same-race PvP enabled using ENEMY_OF_ALL_PLAYERS state
 * - Kill tracking and leaderboard system
 * - Reward distribution based on final ranking
 * - Admin and player command support
 * - Scheduled event activation
 * 
 * <AUTHOR> System
 */
public class FFAService {

    private static final Logger log = LoggerFactory.getLogger(FFAService.class);
    private static final FFAService instance = new FFAService();

    // Event state
    private final AtomicBoolean eventActive = new AtomicBoolean(false);
    
    // Participants management
    private final ConcurrentHashMap<Integer, FFAParticipant> participants = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, FFAParticipant> allEventParticipants = new ConcurrentHashMap<>();

    // FFA Costume system - store original equipment appearance for restoration
    private final ConcurrentHashMap<Integer, List<Item>> originalEquipment = new ConcurrentHashMap<>();
    private static final int FFA_COSTUME_BODY_ID = 110900416; // FFA Body Costume
    private static final int FFA_COSTUME_HEAD_ID = 125045246; // FFA Head Costume

    // Death state tracking (playerId -> death timestamp)
    private final Map<Integer, Long> playerDeathTimes = new ConcurrentHashMap<>();

    // FFA Name system - store original names for restoration
    private final ConcurrentHashMap<Integer, String> originalNames = new ConcurrentHashMap<>();
    private static final String FFA_DISPLAY_NAME = "FFA-Challenger";
    
    // Event instance and timing
    private WorldMapInstance eventInstance;
    private long eventStartTime = 0;
    private Future<?> eventTask;
    private Future<?> announcementTask;
    private Future<?> deathMonitorTask;

    // Map selection
    private List<Integer> availableMaps;
    private int currentEventMapId;

    // Spawn coordinates (legacy support)
    private float spawnX;
    private float spawnY;
    private float spawnZ;
    private byte spawnHeading;

    private FFAService() {
        parseAvailableMaps();
        parseSpawnCoordinates();
    }

    public static FFAService getInstance() {
        return instance;
    }

    /**
     * Initialize the FFA service
     */
    public void initialize() {
        if (!CustomConfig.FFA_ENABLED) {
            log.info("FFA system is disabled in configuration");
            return;
        }

        log.info("Initializing FFA service...");

        // Schedule automatic start if configured
        if (CustomConfig.FFA_AUTO_START) {
            if (!CustomConfig.FFA_SCHEDULE.isEmpty()) {
                // Schedule using cron expression
                CronService.getInstance().schedule(() -> {
                    if (!eventActive.get()) {
                        log.info("Auto-starting FFA event via schedule");
                        startEvent();
                    }
                }, CustomConfig.FFA_SCHEDULE);
                log.info("FFA system scheduled with cron: {}", CustomConfig.FFA_SCHEDULE);
            } else {
                // Start immediately if auto-start is enabled but no schedule
                log.info("Auto-starting FFA event immediately");
                startEvent();
            }
        } else {
            log.info("FFA system requires manual start via admin command //ffa start");
        }

        log.info("FFA service initialized successfully!");
    }

    /**
     * Parse spawn coordinates from configuration
     */
    private void parseSpawnCoordinates() {
        try {
            String[] coords = CustomConfig.FFA_SPAWN_COORDS.split(",");
            if (coords.length >= 3) {
                spawnX = Float.parseFloat(coords[0].trim());
                spawnY = Float.parseFloat(coords[1].trim());
                spawnZ = Float.parseFloat(coords[2].trim());
                spawnHeading = coords.length > 3 ? Byte.parseByte(coords[3].trim()) : 0;
            } else {
                // Default coordinates for Dark Poeta
                spawnX = 242.52405f;
                spawnY = 424.71637f;
                spawnZ = 103.80612f;
                spawnHeading = 0;
            }
        } catch (Exception e) {
            log.error("Failed to parse FFA spawn coordinates, using defaults", e);
            spawnX = 242.52405f;
            spawnY = 424.71637f;
            spawnZ = 103.80612f;
            spawnHeading = 0;
        }
    }

    /**
     * Parse available maps from configuration
     */
    private void parseAvailableMaps() {
        availableMaps = new ArrayList<>();
        try {
            String[] maps = CustomConfig.FFA_MAPS.split(",");
            for (String mapStr : maps) {
                int mapId = Integer.parseInt(mapStr.trim());
                availableMaps.add(mapId);
            }
            log.info("Parsed {} available FFA maps: {}", availableMaps.size(), availableMaps);
        } catch (Exception e) {
            log.error("Failed to parse FFA maps, using default Dark Poeta", e);
            availableMaps.add(300040000); // Default to Dark Poeta
        }

        if (availableMaps.isEmpty()) {
            availableMaps.add(300040000); // Fallback to Dark Poeta
        }
    }

    /**
     * Select a random event map from available maps
     */
    private int selectRandomEventMap() {
        if (availableMaps.isEmpty()) {
            return 300040000; // Default to Dark Poeta
        }
        return Rnd.get(availableMaps);
    }

    /**
     * Get random spawn coordinates for the specified map
     * Returns [x, y, z, heading]
     */
    private float[] getRandomSpawnCoordinates(int mapId) {
        List<float[]> coords = new ArrayList<>();

        switch (mapId) {
            case 300030000: // Nochsana
                coords.add(new float[]{516.2301f, 665.2475f, 330.98575f, 80});
                coords.add(new float[]{505.0609f, 556.5992f, 331.875f, 24});
                coords.add(new float[]{356.20883f, 649.88837f, 374.92065f, 94});
                coords.add(new float[]{361.09503f, 594.8203f, 343.4533f, 110});
                coords.add(new float[]{504.8307f, 552.8712f, 331.73163f, 28});
                coords.add(new float[]{396.7961f, 434.89804f, 367.375f, 49});
                coords.add(new float[]{319.3241f, 482.66284f, 362.1666f, 113});
                coords.add(new float[]{378.74472f, 407.0372f, 376.87595f, 33});
                coords.add(new float[]{361.61758f, 426.42648f, 378.05753f, 73});
                coords.add(new float[]{396.9945f, 284.7902f, 378.83838f, 30});
                coords.add(new float[]{285.6835f, 349.3712f, 380.09332f, 6});
                coords.add(new float[]{337.03836f, 356.89923f, 392.41467f, 30});
                coords.add(new float[]{331.6864f, 271.93954f, 384.55338f, 25});
                coords.add(new float[]{383.3997f, 504.6931f, 360.22946f, 20});
                coords.add(new float[]{430.06927f, 503.45038f, 359.9095f, 70});
                break;

            case 300040000: // Dark Poeta
                coords.add(new float[]{1230.3514f, 409.39297f, 140.125f, 67});
                coords.add(new float[]{1052.1534f, 311.02072f, 132.80185f, 41});
                coords.add(new float[]{972.8223f, 530.3805f, 101.175385f, 78});
                coords.add(new float[]{832.4362f, 577.91846f, 118.75f, 99});
                coords.add(new float[]{641.57635f, 354.30344f, 103.125f, 19});
                coords.add(new float[]{557.3073f, 490.47943f, 107.55174f, 2});
                coords.add(new float[]{517.4669f, 671.3205f, 115.58252f, 8});
                coords.add(new float[]{689.1145f, 861.0198f, 125.9925f, 62});
                coords.add(new float[]{581.9522f, 971.206f, 128.01974f, 79});
                coords.add(new float[]{433.6868f, 10561.9828f, 119.75f, 9}); // Original coordinate as provided
                coords.add(new float[]{561.6822f, 1151.2819f, 139.39552f, 69});
                coords.add(new float[]{334.62918f, 1175.837f, 151.73123f, 31});
                coords.add(new float[]{234.94008f, 1302.3478f, 150.77277f, 114});
                coords.add(new float[]{375.23392f, 1316.1445f, 156.88617f, 80});
                coords.add(new float[]{361.7095f, 1247.0088f, 155.23184f, 97});
                break;

            case 300250000: // Esoterrace
                coords.add(new float[]{844.81775f, 578.80804f, 180.85222f, 110});
                coords.add(new float[]{952.45197f, 424.6489f, 218.1969f, 7});
                coords.add(new float[]{10006.8982f, 514.4989f, 248.64395f, 27}); // Original coordinate as provided
                coords.add(new float[]{967.2982f, 657.34344f, 256.875f, 38});
                coords.add(new float[]{1225.7998f, 483.6569f, 265.76254f, 82});
                coords.add(new float[]{1297.7977f, 609.86475f, 296.66135f, 20});
                coords.add(new float[]{1238.2762f, 577.2078f, 294.89227f, 104});
                coords.add(new float[]{1272.1031f, 774.35925f, 261.0f, 43});
                coords.add(new float[]{1224.0793f, 963.01166f, 326.98068f, 93}); // Fixed typo: was z326.98068
                coords.add(new float[]{1131.2866f, 886.30255f, 316.6489f, 80});
                coords.add(new float[]{1116.6604f, 817.08105f, 316.79816f, 0});
                coords.add(new float[]{1076.9366f, 970.67316f, 322.39038f, 92});
                coords.add(new float[]{1251.3419f, 866.95953f, 320.96628f, 40});
                coords.add(new float[]{1285.4011f, 891.6742f, 318.875f, 39});
                coords.add(new float[]{1026.0908f, 794.0275f, 257.75f, 65});
                break;

            default:
                // Fallback to default Dark Poeta coordinates
                return new float[]{242.52405f, 424.71637f, 103.80612f, 0};
        }

        if (coords.isEmpty()) {
            return new float[]{242.52405f, 424.71637f, 103.80612f, 0};
        }

        return Rnd.get(coords);
    }

    /**
     * Start the FFA event
     */
    public void startEvent() {
        if (!CustomConfig.FFA_ENABLED) {
            log.info("FFA system is disabled in configuration");
            return;
        }

        if (eventActive.get()) {
            log.warn("FFA event is already active");
            return;
        }

        // Select random map for this event
        currentEventMapId = selectRandomEventMap();
        log.info("Starting FFA event on randomly selected map: {}", currentEventMapId);

        try {
            // Create event instance using the randomly selected map
            eventInstance = InstanceService.getNextAvailableInstance(currentEventMapId, 0, (byte) 0,
                CustomConfig.FFA_MAX_PLAYERS, true);
            if (eventInstance == null) {
                log.error("Failed to create FFA event instance for map {} - instance creation returned null", currentEventMapId);
                log.error("Make sure map {} is a valid instance map that supports instancing", currentEventMapId);
                return;
            }

            // Clear all monsters from the instance to create a clean PvP environment
            // Add a small delay to ensure instance is fully loaded before clearing
            ThreadPoolManager.getInstance().schedule(() -> {
                clearInstanceMonsters();
            }, 2000); // 2 second delay

        } catch (Exception e) {
            log.error("Exception occurred while creating FFA event instance for map {}: {}", currentEventMapId, e.getMessage());
            log.error("Make sure map {} is a valid instance map (like 300020000, 300030000, 300040000)", currentEventMapId);
            return;
        }

        eventActive.set(true);
        eventStartTime = System.currentTimeMillis();

        // Announce event start
        announceEventStart();

        // Schedule event end
        eventTask = ThreadPoolManager.getInstance().schedule(() -> {
            endEvent();
        }, CustomConfig.FFA_DURATION_MINUTES * 60 * 1000);

        // Start death state monitoring (every 5 seconds)
        deathMonitorTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            monitorDeathStates();
        }, 5000, 5000);

        log.info("FFA event started on map {} with instance {} with death monitoring active", currentEventMapId,
            eventInstance.getInstanceId());
    }

    /**
     * End the FFA event
     */
    public void endEvent() {
        if (!eventActive.get()) {
            return;
        }

        log.info("Ending FFA event");

        // Calculate and distribute rewards
        distributeRewards();

        // Remove all participants from event
        removeAllParticipants();

        // Cleanup
        cleanup();

        // Announce event end
        announceEventEnd();
    }

    /**
     * Force start event (admin command)
     */
    public void forceStartEvent() {
        if (!eventActive.get()) {
            log.info("Force starting FFA event");
            startEvent();
        }
    }

    /**
     * Force end event (admin command)
     */
    public void forceEndEvent() {
        if (eventActive.get()) {
            log.info("Force ending FFA event");
            endEvent();
        }
    }

    /**
     * Cleanup event resources
     */
    private void cleanup() {
        eventActive.set(false);
        eventStartTime = 0;
        
        if (eventTask != null && !eventTask.isCancelled()) {
            eventTask.cancel(true);
            eventTask = null;
        }
        
        if (announcementTask != null && !announcementTask.isCancelled()) {
            announcementTask.cancel(true);
            announcementTask = null;
        }

        if (deathMonitorTask != null && !deathMonitorTask.isCancelled()) {
            deathMonitorTask.cancel(true);
            deathMonitorTask = null;
        }
        
        if (eventInstance != null) {
            // Instance cleanup will be handled by the system
            eventInstance = null;
        }
        
        participants.clear();
        allEventParticipants.clear();

        // Clear costume data
        originalEquipment.clear();

        // Clear death tracking
        playerDeathTimes.clear();

        // Clear name data
        originalNames.clear();

        log.info("FFA event cleanup completed");
    }

    /**
     * Apply FFA costume directly without chat spam
     */
    private void applyFFACostume(Player player) {
        try {
            // Store original equipment for restoration
            List<Item> originalItems = new ArrayList<>(player.getEquipment().getEquippedForAppearance());
            originalEquipment.put(player.getObjectId(), originalItems);

            // Apply costumes directly using the same logic as Preview command but without chat messages
            applyCostumeSilent(player);

            // Schedule a task to keep reapplying the costume every 8 seconds to keep it persistent
            // This ensures the costume stays active throughout the FFA event
            ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
                if (participants.containsKey(player.getObjectId()) && player.isOnline()) {
                    // Reapply costume silently
                    applyCostumeSilent(player);
                }
            }, 8000, 8000); // Every 8 seconds (before the 10-second reset)

        } catch (Exception e) {
            log.error("Failed to apply FFA costume to player {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * Apply costume silently without chat messages (based on Preview command logic)
     */
    private void applyCostumeSilent(Player player) {
        try {
            // Get item templates for both costumes
            com.aionemu.gameserver.model.templates.item.ItemTemplate bodyTemplate =
                com.aionemu.gameserver.dataholders.DataManager.ITEM_DATA.getItemTemplate(FFA_COSTUME_BODY_ID);
            com.aionemu.gameserver.model.templates.item.ItemTemplate headTemplate =
                com.aionemu.gameserver.dataholders.DataManager.ITEM_DATA.getItemTemplate(FFA_COSTUME_HEAD_ID);

            if (bodyTemplate == null || headTemplate == null) {
                log.error("Failed to find costume templates - Body: {}, Head: {}", bodyTemplate != null, headTemplate != null);
                return;
            }

            // Create preview items list
            List<com.aionemu.gameserver.model.gameobjects.Item> previewItems = new ArrayList<>();
            long previewItemsSlotMask = 0;

            // Add body costume
            long bodySlotMask = bodyTemplate.getItemSlot();
            if (com.aionemu.gameserver.model.items.ItemSlot.isVisible(bodySlotMask)) {
                com.aionemu.gameserver.model.gameobjects.Item bodyItem =
                    new com.aionemu.gameserver.model.gameobjects.Item(0, bodyTemplate, 1, true, bodySlotMask);
                previewItems.add(bodyItem);
                previewItemsSlotMask |= bodySlotMask;
            }

            // Add head costume
            long headSlotMask = headTemplate.getItemSlot();
            if (com.aionemu.gameserver.model.items.ItemSlot.isVisible(headSlotMask)) {
                com.aionemu.gameserver.model.gameobjects.Item headItem =
                    new com.aionemu.gameserver.model.gameobjects.Item(0, headTemplate, 1, true, headSlotMask);
                previewItems.add(headItem);
                previewItemsSlotMask |= headSlotMask;
            }

            // Add player's existing equipment (except for slots occupied by costumes)
            for (com.aionemu.gameserver.model.gameobjects.Item visibleEquipment : player.getEquipment().getEquippedForAppearance()) {
                if ((visibleEquipment.getEquipmentSlot() & previewItemsSlotMask) == 0) {
                    previewItems.add(visibleEquipment);
                }
            }

            // Sort items by equipment slot to avoid display bugs
            previewItems.sort(java.util.Comparator.comparingLong(com.aionemu.gameserver.model.gameobjects.Item::getEquipmentSlot));

            // Apply costume appearance
            int display = player.getPlayerSettings().getDisplay() | com.aionemu.gameserver.network.aion.serverpackets.SM_CUSTOM_SETTINGS.HIDE_LEGION_CLOAK;
            if (previewItems.stream().anyMatch(item -> item.getEquipmentSlot() == com.aionemu.gameserver.model.items.ItemSlot.HELMET.getSlotIdMask())) {
                display &= ~com.aionemu.gameserver.network.aion.serverpackets.SM_CUSTOM_SETTINGS.HIDE_HELMET;
            }
            if (previewItems.stream().anyMatch(item -> item.getEquipmentSlot() == com.aionemu.gameserver.model.items.ItemSlot.PLUME.getSlotIdMask())) {
                display &= ~com.aionemu.gameserver.network.aion.serverpackets.SM_CUSTOM_SETTINGS.HIDE_PLUME;
            }

            // Send appearance update packets (no chat messages)
            com.aionemu.gameserver.utils.PacketSendUtility.sendPacket(player,
                new com.aionemu.gameserver.network.aion.serverpackets.SM_CUSTOM_SETTINGS(player.getObjectId(), 1, display, player.getPlayerSettings().getDeny()));
            com.aionemu.gameserver.utils.PacketSendUtility.sendPacket(player,
                new com.aionemu.gameserver.network.aion.serverpackets.SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), previewItems));

            log.debug("Successfully applied FFA costume silently to player: {}", player.getName());

        } catch (Exception e) {
            log.error("Failed to apply FFA costume silently to player {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * Remove FFA costume from player (restore original appearance like Preview reset)
     */
    private void removeFFACostume(Player player) {
        try {
            // Get stored original equipment
            List<Item> originalItems = originalEquipment.remove(player.getObjectId());
            if (originalItems == null) {
                originalItems = player.getEquipment().getEquippedForAppearance();
            }

            // Restore appearance exactly like Preview command reset
            PacketSendUtility.sendPacket(player, new SM_CUSTOM_SETTINGS(player));
            PacketSendUtility.sendPacket(player, new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), originalItems));

        } catch (Exception e) {
            log.error("Failed to remove FFA costume from player {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * Apply FFA display name to player
     */
    private void applyFFAName(Player player) {
        try {
            // Store original name for restoration
            String originalName = player.getName();
            originalNames.put(player.getObjectId(), originalName);

            // Set temporary display name
            player.getCommonData().setName(FFA_DISPLAY_NAME);

            // Update world cache
            World.getInstance().updateCachedPlayerName(originalName, player);

            // Broadcast name change to all players
            PacketSendUtility.broadcastToWorld(new SM_RENAME(player, originalName));

        } catch (Exception e) {
            log.error("Failed to apply FFA name to player {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * Remove FFA display name from player (restore original name)
     */
    private void removeFFAName(Player player) {
        try {
            String currentDisplayName = player.getName();

            // Get stored original name
            String originalName = originalNames.remove(player.getObjectId());
            if (originalName == null) {
                return;
            }

            // Restore original name
            player.getCommonData().setName(originalName);

            // Update world cache
            World.getInstance().updateCachedPlayerName(currentDisplayName, player);

            // Broadcast name change to all players
            PacketSendUtility.broadcastToWorld(new SM_RENAME(player, currentDisplayName));

        } catch (Exception e) {
            log.error("Failed to remove FFA name from player {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * Announce event start
     */
    private void announceEventStart() {
        if (CustomConfig.FFA_ANNOUNCEMENTS) {
            String message = "FFA Event has started! Use .ffa to join the battle!";
            World.getInstance().getAllPlayers().forEach(player -> {
                if (player.getLevel() >= CustomConfig.FFA_MIN_LEVEL) {
                    PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
                }
            });
        }
    }

    /**
     * Announce event end
     */
    private void announceEventEnd() {
        if (CustomConfig.FFA_ANNOUNCEMENTS) {
            String message = "FFA Event has ended! Check your mail for rewards.";
            World.getInstance().getAllPlayers().forEach(player -> {
                PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
            });
        }
    }

    /**
     * Announce winners at the end of the event
     */
    private void announceWinners(List<FFAParticipant> leaderboard) {
        if (!CustomConfig.FFA_ANNOUNCEMENTS || leaderboard.isEmpty()) {
            return;
        }

        StringBuilder announcement = new StringBuilder();
        announcement.append("=== FFA Event Results ===\n");

        // Announce top 3 winners
        int maxWinners = Math.min(3, leaderboard.size());
        for (int i = 0; i < maxWinners; i++) {
            FFAParticipant winner = leaderboard.get(i);
            String place = switch (i) {
                case 0 -> "1st";
                case 1 -> "2nd";
                case 2 -> "3rd";
                default -> (i + 1) + "th";
            };

            announcement.append(String.format("%s Place: %s (%d kills)\n",
                place, winner.getPlayerName(), winner.getKills()));
        }

        announcement.append("Congratulations to all participants!");

        // Broadcast to all players
        String finalMessage = announcement.toString();
        World.getInstance().getAllPlayers().forEach(player -> {
            PacketSendUtility.sendMessage(player, finalMessage, ChatType.BRIGHT_YELLOW_CENTER);
        });

        log.info("FFA Event winners announced: {}", finalMessage.replace("\n", " | "));
    }

    /**
     * Distribute rewards based on final ranking
     */
    private void distributeRewards() {
        if (allEventParticipants.isEmpty()) {
            log.info("No participants to reward");
            return;
        }

        log.info("Distributing FFA rewards to {} participants", allEventParticipants.size());

        // Get final leaderboard (sorted by kills)
        List<FFAParticipant> finalLeaderboard = getLeaderboard();

        // Announce winners
        announceWinners(finalLeaderboard);

        for (int i = 0; i < finalLeaderboard.size(); i++) {
            FFAParticipant participant = finalLeaderboard.get(i);
            int rank = i + 1; // 1-based ranking
            String playerName = participant.getPlayerName();

            // Send rewards based on rank
            sendRewards(playerName, rank, participant.getKills());

            log.debug("Rewarded player {} (Rank: {}, Kills: {})",
                playerName, rank, participant.getKills());
        }
    }

    /**
     * Send rewards to a participant based on their rank
     */
    private void sendRewards(String playerName, int rank, int kills) {
        String rewardConfig;
        String title = "FFA Event Rewards";
        String message;

        // Determine reward based on rank
        if (rank == 1) {
            rewardConfig = CustomConfig.FFA_REWARDS_FIRST;
            message = String.format("Congratulations! You won the FFA Event with %d kills! You finished in 1st place.", kills);
        } else if (rank == 2) {
            rewardConfig = CustomConfig.FFA_REWARDS_SECOND;
            message = String.format("Great job! You finished 2nd in the FFA Event with %d kills!", kills);
        } else if (rank == 3) {
            rewardConfig = CustomConfig.FFA_REWARDS_THIRD;
            message = String.format("Well done! You finished 3rd in the FFA Event with %d kills!", kills);
        } else {
            rewardConfig = CustomConfig.FFA_REWARDS_PARTICIPATION;
            message = String.format("Thank you for participating in the FFA Event! You finished rank #%d with %d kills.", rank, kills);
        }

        // Parse reward configuration (itemId:itemCount:kinah)
        int[] rewardData = parseRewardConfig(rewardConfig);
        if (rewardData == null) {
            log.error("Failed to parse reward configuration for rank {}: {}", rank, rewardConfig);
            return;
        }

        // Send reward via mail
        boolean mailSent = SystemMailService.sendMail("FFA System", playerName, title, message,
            rewardData[0], rewardData[1], rewardData[2], LetterType.EXPRESS);

        if (mailSent) {
            log.debug("Sent FFA rewards to {} (Rank: {}, ItemId: {}, Count: {}, Kinah: {})",
                playerName, rank, rewardData[0], rewardData[1], rewardData[2]);
        } else {
            log.error("Failed to send FFA rewards to {} (Rank: {})", playerName, rank);
        }
    }

    /**
     * Parse reward configuration string (itemId:itemCount:kinah)
     */
    private int[] parseRewardConfig(String config) {
        try {
            String[] parts = config.split(":");
            if (parts.length >= 3) {
                return new int[] {
                    Integer.parseInt(parts[0].trim()), // itemId
                    Integer.parseInt(parts[1].trim()), // itemCount
                    Integer.parseInt(parts[2].trim())  // kinah
                };
            }
        } catch (Exception e) {
            log.error("Failed to parse reward configuration: {}", config, e);
        }
        return null;
    }

    /**
     * Remove all participants from event
     */
    private void removeAllParticipants() {
        log.info("Removing all FFA participants and teleporting them out");

        for (FFAParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null && player.isOnline()) {
                // Resurrect if dead before removing (like 1v1 system)
                if (player.isDead()) {
                    try {
                        // Force resurrect with full HP/MP first
                        com.aionemu.gameserver.services.player.PlayerReviveService.revive(player, 100, 100, false, 0);

                        // Clear any resurrection dialogs or states after revive (like 1v1 system)
                        player.unsetResPosState();
                        player.setPlayerResActivate(false);

                        // Update stats
                        if (player.getGameStats() != null) {
                            player.getGameStats().updateStatsAndSpeedVisually();
                        }

                        log.debug("Resurrected dead FFA player {} for event end teleportation", player.getName());
                    } catch (Exception e) {
                        log.error("Failed to resurrect FFA player {} during event end: {}", player.getName(), e.getMessage());
                    }
                }

                removeParticipant(player, false);
            }
        }

        participants.clear();
    }

    /**
     * Check if player can join FFA event
     */
    public boolean canJoinEvent(Player player) {
        if (!CustomConfig.FFA_ENABLED || !eventActive.get()) {
            return false;
        }

        if (player.getLevel() < CustomConfig.FFA_MIN_LEVEL) {
            return false;
        }

        if (participants.size() >= CustomConfig.FFA_MAX_PLAYERS) {
            return false;
        }

        // Check if player is already actively participating (not eliminated)
        if (participants.containsKey(player.getObjectId())) {
            return false;
        }

        // Players can re-join even if they participated before (were eliminated)
        return true;
    }

    /**
     * Add player to FFA event
     */
    public boolean joinEvent(Player player) {
        if (!canJoinEvent(player)) {
            return false;
        }

        log.debug("Player {} joining FFA event", player.getName());

        // Check if player has participated before (preserve kill count)
        FFAParticipant participant = allEventParticipants.get(player.getObjectId());
        if (participant == null) {
            // New participant
            participant = new FFAParticipant(player.getObjectId(), player.getName());
            allEventParticipants.put(player.getObjectId(), participant);
        } else {
            // Returning participant - reset join time but preserve kills
            participant.rejoin();
        }

        // Add to active participants
        participants.put(player.getObjectId(), participant);

        // Set FFA participant state for same-race PvP - ALL players are enemies
        player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();

        // Ensure all existing participants see this player as enemy and vice versa
        updateAllPlayersEnemyState();

        // Remove any teleportation protection after a short delay
        ThreadPoolManager.getInstance().schedule(() -> {
            if (player.isOnline() && participants.containsKey(player.getObjectId())) {
                player.unsetCustomState(CustomPlayerState.INVULNERABLE);
                player.unsetCustomState(CustomPlayerState.TELEPORTATION_MODE);
                player.getController().onChangedPlayerAttributes();

                // Re-ensure enemy state after teleportation protection is removed
                player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                player.getController().onChangedPlayerAttributes();
                log.debug("Confirmed ENEMY_OF_ALL_PLAYERS state for player {} after teleportation protection", player.getName());
            }
        }, 3000); // 3 second delay

        // Apply FFA costume
        log.debug("Applying FFA costume to player: {}", player.getName());
        applyFFACostume(player);

        // Apply FFA display name
        applyFFAName(player);

        // Teleport to event map
        if (eventInstance != null) {
            teleportToEventMap(player);
        }

        PacketSendUtility.sendMessage(player, "You have joined the FFA Event! Fight for glory!",
            ChatType.BRIGHT_YELLOW_CENTER);

        log.debug("Player {} joined FFA event", player.getName());
        return true;
    }

    /**
     * Remove player from FFA event
     */
    public boolean leaveEvent(Player player) {
        FFAParticipant participant = participants.get(player.getObjectId());
        if (participant == null) {
            return false;
        }

        removeParticipant(player, true);
        return true;
    }

    /**
     * Remove participant from FFA event (for external services like disconnect handling)
     */
    public void removeParticipant(Player player) {
        if (participants.containsKey(player.getObjectId())) {
            removeParticipant(player, false); // false = involuntary (disconnect)
        }
    }

    /**
     * Remove participant from event
     */
    private void removeParticipant(Player player, boolean voluntary) {
        participants.remove(player.getObjectId());

        // Remove FFA costume
        removeFFACostume(player);

        // Remove FFA display name
        removeFFAName(player);

        // Remove FFA participant state - IMPORTANT: This allows same-race players to be friendly again
        player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();

        log.debug("Removed ENEMY_OF_ALL_PLAYERS state from player {} - same-race PvP disabled", player.getName());

        // Teleport out of event instance if still inside
        if (eventInstance != null && player.getInstanceId() == eventInstance.getInstanceId()) {
            teleportToCapitalCity(player);
        }

        String message = voluntary ? "You have left the FFA Event." : "You have been removed from the FFA Event.";
        PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);

        log.debug("Player {} removed from FFA event (voluntary: {})", player.getName(), voluntary);
    }

    /**
     * Handle player kill in FFA event
     */
    public void onPlayerKill(Player killer, Player victim) {
        if (!eventActive.get()) {
            return;
        }

        FFAParticipant killerParticipant = participants.get(killer.getObjectId());
        if (killerParticipant == null) {
            return;
        }

        // Increment kills for the killer
        killerParticipant.incrementKills();

        log.debug("FFA kill recorded: {} killed {} (Total kills: {})",
            killer.getName(), victim.getName(), killerParticipant.getKills());

        // Send kill notification to killer
        PacketSendUtility.sendMessage(killer,
            String.format("Kill confirmed! Total kills: %d", killerParticipant.getKills()),
            ChatType.BRIGHT_YELLOW_CENTER);

        // Handle victim death - remove from active participants but keep in allEventParticipants
        handlePlayerDeath(victim);

        // Broadcast leaderboard update periodically
        if (killerParticipant.getKills() % 5 == 0) { // Every 5 kills
            broadcastLeaderboard();
        }
    }

    /**
     * Handle player death in FFA event
     * This method only handles post-death logic - resurrection is handled by instance handler
     */
    public void handlePlayerDeath(Player victim) {
        if (!eventActive.get()) {
            return;
        }

        FFAParticipant victimParticipant = participants.get(victim.getObjectId());
        if (victimParticipant == null) {
            return; // Player wasn't actively participating
        }

        log.debug("Handling FFA death for player: {} (resurrection handled by instance handler)", victim.getName());

        try {
            // Only handle post-death logic - let instance handler do the resurrection
            // Ensure FFA enemy state is maintained after resurrection
            ThreadPoolManager.getInstance().schedule(() -> {
                if (victim.isOnline() && participants.containsKey(victim.getObjectId())) {
                    // Ensure FFA enemy state is maintained
                    victim.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                    victim.getController().onChangedPlayerAttributes();

                    log.debug("Maintained FFA enemy state for player {} after resurrection", victim.getName());
                }
            }, 1000); // 1 second delay to ensure resurrection is complete

        } catch (Exception e) {
            log.error("FFA: Error handling player death for {}: {}", victim.getName(), e.getMessage(), e);
        }
    }

    /**
     * Handle player disconnect during FFA event
     * This ensures proper cleanup of FFA states when players disconnect
     */
    public void handlePlayerDisconnect(Player player) {
        if (!eventActive.get()) {
            return;
        }

        FFAParticipant participant = participants.get(player.getObjectId());
        if (participant == null) {
            // Check if player has FFA name even if not in active participants
            if (originalNames.containsKey(player.getObjectId())) {
                log.info("Cleaning up FFA name for disconnected player: {}", player.getName());
                removeFFAName(player);
            }
            return;
        }

        log.info("Handling FFA disconnect for player: {}", player.getName());

        // Remove from active participants (preserve kill count in allEventParticipants)
        participants.remove(player.getObjectId());

        // Clean up FFA states
        removeFFACostume(player);
        removeFFAName(player);

        // Remove FFA participant state
        player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();

        // Clean up death tracking
        playerDeathTimes.remove(player.getObjectId());

        log.debug("Cleaned up FFA states for disconnected player: {}", player.getName());
    }

    /**
     * Check and restore player name if they have a stuck FFA name from previous session
     * Called when player logs in to ensure proper name restoration
     */
    public void checkAndRestorePlayerName(Player player) {
        try {
            // Check if player's current name is the FFA display name
            if (FFA_DISPLAY_NAME.equals(player.getName())) {
                log.warn("Player {} has stuck FFA name '{}' on login, attempting to restore original name",
                        player.getObjectId(), FFA_DISPLAY_NAME);

                // Try to get original name from database
                String originalName = com.aionemu.gameserver.dao.PlayerDAO.getPlayerNameByObjId(player.getObjectId());

                if (originalName != null && !FFA_DISPLAY_NAME.equals(originalName)) {
                    // Restore the original name
                    player.getCommonData().setName(originalName);

                    // Update world cache
                    World.getInstance().updateCachedPlayerName(FFA_DISPLAY_NAME, player);

                    // Broadcast name change to all players
                    PacketSendUtility.broadcastToWorld(new SM_RENAME(player, FFA_DISPLAY_NAME));

                    log.info("Successfully restored original name '{}' for player {} who had stuck FFA name",
                            originalName, player.getObjectId());
                } else {
                    log.error("Could not restore original name for player {} - original name not found or is also FFA name",
                            player.getObjectId());
                }
            }
        } catch (Exception e) {
            log.error("Error checking/restoring FFA name for player {}: {}", player.getObjectId(), e.getMessage(), e);
        }
    }

    /**
     * Get current leaderboard sorted by kills (includes all participants, not just active ones)
     */
    public List<FFAParticipant> getLeaderboard() {
        return allEventParticipants.values().stream()
            .sorted((p1, p2) -> Integer.compare(p2.getKills(), p1.getKills()))
            .collect(Collectors.toList());
    }

    /**
     * Get top participants
     */
    public List<FFAParticipant> getTopParticipants(int count) {
        return getLeaderboard().stream()
            .limit(count)
            .collect(Collectors.toList());
    }

    /**
     * Get participant rank
     */
    public int getParticipantRank(int playerId) {
        List<FFAParticipant> leaderboard = getLeaderboard();
        for (int i = 0; i < leaderboard.size(); i++) {
            if (leaderboard.get(i).getPlayerId() == playerId) {
                return i + 1; // 1-based ranking
            }
        }
        return -1; // Not found
    }

    /**
     * Broadcast leaderboard to all participants
     */
    public void broadcastLeaderboard() {
        if (!eventActive.get()) {
            return;
        }

        List<FFAParticipant> top5 = getTopParticipants(5);
        StringBuilder message = new StringBuilder("FFA Leaderboard (Top 5):\n");

        for (int i = 0; i < top5.size(); i++) {
            FFAParticipant participant = top5.get(i);
            message.append(String.format("%d. %s - %d kills\n",
                i + 1, participant.getPlayerName(), participant.getKills()));
        }

        // Send to all participants
        for (FFAParticipant participant : participants.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null && player.isOnline()) {
                PacketSendUtility.sendMessage(player, message.toString(), ChatType.BRIGHT_YELLOW_CENTER);
            }
        }
    }

    /**
     * Get leaderboard as formatted string
     */
    public String getLeaderboardString(int count) {
        List<FFAParticipant> leaderboard = getTopParticipants(count);
        if (leaderboard.isEmpty()) {
            return "No participants found.";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("=== FFA Leaderboard (Top ").append(count).append(") ===\n");

        for (int i = 0; i < leaderboard.size(); i++) {
            FFAParticipant participant = leaderboard.get(i);
            sb.append(String.format("%d. %s - %d kills\n",
                i + 1, participant.getPlayerName(), participant.getKills()));
        }

        return sb.toString();
    }

    /**
     * Get participants as formatted string
     */
    public String getParticipantsString() {
        if (allEventParticipants.isEmpty()) {
            return "No participants found.";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("=== FFA Participants ===\n");
        sb.append("Active: ").append(participants.size()).append(" | ");
        sb.append("Total: ").append(allEventParticipants.size()).append("\n\n");

        sb.append("All Participants (sorted by kills):\n");
        List<FFAParticipant> sortedParticipants = getLeaderboard();

        for (int i = 0; i < sortedParticipants.size(); i++) {
            FFAParticipant p = sortedParticipants.get(i);
            boolean isActive = participants.containsKey(p.getPlayerId());
            sb.append(String.format("%d. %s - %d kills %s\n",
                i + 1, p.getPlayerName(), p.getKills(),
                isActive ? "(Active)" : "(Eliminated)"));
        }

        return sb.toString();
    }

    /**
     * Teleport player to event map using random spawn location
     * Note: For death resurrections, the instance handler now handles teleportation
     * This method is used for initial joining and manual resurrections
     */
    private void teleportToEventMap(Player player) {
        if (eventInstance == null) {
            log.error("Cannot teleport player {} - event instance is null", player.getName());
            return;
        }

        try {
            // Get random spawn coordinates based on the current map
            float[] coords = getRandomSpawnCoordinates(currentEventMapId);

            // Add slight delay to prevent rollback issues
            ThreadPoolManager.getInstance().schedule(() -> {
                TeleportService.teleportTo(player, eventInstance, coords[0], coords[1], coords[2], (byte) coords[3]);
                log.debug("Teleported player {} to FFA event map {} at random coordinates ({}, {}, {})",
                    player.getName(), currentEventMapId, coords[0], coords[1], coords[2]);
            }, 100); // 100ms delay

        } catch (Exception e) {
            log.error("Failed to teleport player {} to FFA event map", player.getName(), e);
        }
    }

    /**
     * Teleport player to capital city
     */
    private void teleportToCapitalCity(Player player) {
        try {
            if (player.getRace() == com.aionemu.gameserver.model.Race.ELYOS) {
                TeleportService.teleportTo(player, 110010000, 1313.25f, 1512.011f, 568.107f, (byte) 0);
            } else {
                TeleportService.teleportTo(player, 120010000, 1685.7f, 1400.5f, 195.48618f, (byte) 60);
            }
            log.debug("Teleported player {} back to capital city", player.getName());
        } catch (Exception e) {
            log.error("Failed to teleport player {} to capital city", player.getName(), e);
        }
    }

    /**
     * Clear all monsters from the FFA event instance to create a clean PvP environment
     */
    private void clearInstanceMonsters() {
        if (eventInstance == null) {
            return;
        }

        try {
            int clearedCount = 0;

            // Get all NPCs in the instance and despawn them
            for (Npc npc : eventInstance.getNpcs()) {
                if (npc != null && !npc.isDead()) {
                    try {
                        // Despawn the NPC
                        npc.getController().delete();
                        clearedCount++;
                        log.debug("Despawned NPC {} from FFA instance", npc.getNpcId());
                    } catch (Exception e) {
                        log.debug("Failed to despawn NPC {}: {}", npc.getNpcId(), e.getMessage());
                    }
                }
            }

            log.debug("Cleared {} monsters from FFA instance {} - Clean PvP environment ready!",
                clearedCount, eventInstance.getInstanceId());

        } catch (Exception e) {
            log.warn("Failed to clear monsters from FFA instance: {}", e.getMessage());
        }
    }

    // Getters for status checking
    public boolean isEventActive() {
        return eventActive.get();
    }

    /**
     * Check if a player is currently participating in an FFA event
     */
    public boolean isPlayerInEvent(Player player) {
        return participants.containsKey(player.getObjectId());
    }

    public boolean isEnabled() {
        return CustomConfig.FFA_ENABLED;
    }

    public int getParticipantCount() {
        return participants.size(); // Active participants
    }

    public int getTotalParticipantCount() {
        return allEventParticipants.size(); // All participants who have ever joined
    }

    public long getEventStartTime() {
        return eventStartTime;
    }

    public WorldMapInstance getEventInstance() {
        return eventInstance;
    }

    public int getCurrentEventMapId() {
        return currentEventMapId;
    }

    /**
     * Update all FFA players to ensure they all see each other as enemies
     */
    private void updateAllPlayersEnemyState() {
        try {
            for (FFAParticipant participant : participants.values()) {
                Player player = World.getInstance().getPlayer(participant.getPlayerId());
                if (player != null && player.isOnline()) {
                    // Ensure ENEMY_OF_ALL_PLAYERS state is set
                    player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                    player.getController().onChangedPlayerAttributes();
                }
            }
            log.debug("Updated enemy state for all {} FFA participants", participants.size());
        } catch (Exception e) {
            log.error("Error updating enemy states for FFA players: {}", e.getMessage(), e);
        }
    }

    /**
     * Monitor players for stuck death states and automatically fix them
     */
    public void monitorDeathStates() {
        if (!eventActive.get() || participants.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        final long DEATH_TIMEOUT_MS = 15000; // 15 seconds

        try {
            // Check all active FFA participants for death state issues
            for (FFAParticipant participant : participants.values()) {
                Player player = World.getInstance().getPlayer(participant.getPlayerId());
                if (player == null || !player.isOnline()) {
                    // Clean up death tracking for offline players
                    playerDeathTimes.remove(participant.getPlayerId());
                    continue;
                }

                // Check if player is dead
                if (player.isDead()) {
                    Long deathTime = playerDeathTimes.get(participant.getPlayerId());

                    if (deathTime == null) {
                        // First time we detect this player is dead, record the time
                        playerDeathTimes.put(participant.getPlayerId(), currentTime);
                        log.debug("FFA: Detected player {} is dead, starting death timer", player.getName());
                    } else {
                        // Check if player has been dead too long
                        long deadDuration = currentTime - deathTime;
                        if (deadDuration >= DEATH_TIMEOUT_MS) {
                            log.warn("FFA: Player {} has been stuck in death state for {}ms, auto-fixing",
                                    player.getName(), deadDuration);

                            // Auto-fix the stuck death state for FFA
                            autoFixStuckDeathStateInFFA(player, participant);

                            // Remove from death tracking
                            playerDeathTimes.remove(participant.getPlayerId());
                        }
                    }
                } else {
                    // Player is alive, remove from death tracking if present
                    if (playerDeathTimes.remove(participant.getPlayerId()) != null) {
                        log.debug("FFA: Player {} is no longer dead, removed from death tracking", player.getName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error monitoring death states in FFA: {}", e.getMessage(), e);
        }
    }

    /**
     * Automatically fix a player stuck in death state in FFA
     */
    private void autoFixStuckDeathStateInFFA(Player player, FFAParticipant participant) {
        try {
            log.info("FFA: Auto-fixing stuck death state for player {}", player.getName());

            // Force resurrect with full HP/MP first
            com.aionemu.gameserver.services.player.PlayerReviveService.revive(player, 100, 100, false, 0);

            // Clear any resurrection dialogs or states after revive (like 1v1 system)
            player.unsetResPosState();
            player.setPlayerResActivate(false);

            // Reset all cooldowns (like //removecd command)
            resetPlayerCooldowns(player);

            // Update stats and send resurrection message
            if (player.getGameStats() != null) {
                player.getGameStats().updateStatsAndSpeedVisually();
            }

            // Send resurrection message
            PacketSendUtility.sendPacket(player, com.aionemu.gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE.STR_REBIRTH_MASSAGE_ME());

            // Remove any death-related emotions
            PacketSendUtility.broadcastPacket(player, new com.aionemu.gameserver.network.aion.serverpackets.SM_EMOTION(player, com.aionemu.gameserver.model.EmotionType.RESURRECT), true);

            // Ensure FFA enemy state is maintained
            player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
            player.getController().onChangedPlayerAttributes();

            // Teleport to random spawn location in the FFA instance
            if (eventInstance != null && player.getInstanceId() == eventInstance.getInstanceId()) {
                teleportToEventMap(player);
            }

            // Send message to player
            PacketSendUtility.sendMessage(player,
                "You were stuck in death state and have been automatically resurrected with full HP/MP and reset cooldowns in the FFA arena.",
                ChatType.BRIGHT_YELLOW_CENTER);

            log.info("FFA: Successfully auto-fixed death state for player {} with full restoration", player.getName());

        } catch (Exception e) {
            log.error("FFA: Error auto-fixing stuck death state for player {}: {}", player.getName(), e.getMessage(), e);
        }
    }

    /**
     * Reset all cooldowns for a player (like //removecd command)
     */
    private void resetPlayerCooldowns(Player player) {
        try {
            // Reset skill cooldowns
            if (player.getSkillCoolDowns() != null) {
                player.getSkillCoolDowns().clear();
            }

            // Reset item cooldowns
            if (player.getItemCoolDowns() != null) {
                player.getItemCoolDowns().clear();
            }

            // Send cooldown reset packet
            PacketSendUtility.sendPacket(player, new com.aionemu.gameserver.network.aion.serverpackets.SM_SKILL_COOLDOWN(player.getSkillCoolDowns()));

            log.debug("Reset all cooldowns for FFA player {}", player.getName());
        } catch (Exception e) {
            log.error("Error resetting cooldowns for FFA player {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * Manual resurrection command for stuck players in FFA
     */
    public void manualResurrectPlayer(Player player) {
        if (!isEventActive() || participants.get(player.getObjectId()) == null) {
            PacketSendUtility.sendMessage(player, "You are not in an active FFA event.", ChatType.BRIGHT_YELLOW_CENTER);
            return;
        }

        try {
            // Force resurrect with full HP/MP first
            com.aionemu.gameserver.services.player.PlayerReviveService.revive(player, 100, 100, false, 0);

            // Clear any resurrection dialogs or states after revive (like 1v1 system)
            player.unsetResPosState();
            player.setPlayerResActivate(false);

            // Remove soul sickness effect
            player.getEffectController().removeEffect(8291); // Soul Sickness effect ID

            // Reset all cooldowns
            resetPlayerCooldowns(player);

            // Update stats and send resurrection message
            if (player.getGameStats() != null) {
                player.getGameStats().updateStatsAndSpeedVisually();
            }

            // Send resurrection message
            PacketSendUtility.sendPacket(player, com.aionemu.gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE.STR_REBIRTH_MASSAGE_ME());

            // Remove any death-related emotions
            PacketSendUtility.broadcastPacket(player, new com.aionemu.gameserver.network.aion.serverpackets.SM_EMOTION(player, com.aionemu.gameserver.model.EmotionType.RESURRECT), true);

            // Ensure FFA enemy state is maintained
            player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
            player.getController().onChangedPlayerAttributes();

            // Teleport to random spawn location
            teleportToEventMap(player);

            // Send success message
            PacketSendUtility.sendMessage(player,
                "You have been manually resurrected with full HP/MP and reset cooldowns in the FFA arena.",
                ChatType.BRIGHT_YELLOW_CENTER);

            log.info("FFA: Manual resurrection performed for player {}", player.getName());

        } catch (Exception e) {
            log.error("FFA: Error manually resurrecting player {}: {}", player.getName(), e.getMessage(), e);
            PacketSendUtility.sendMessage(player, "Error during manual resurrection. Please contact an administrator.", ChatType.BRIGHT_YELLOW_CENTER);
        }
    }

    /**
     * Inner class for participant data
     */
    public static class FFAParticipant {
        private final int playerId;
        private final String playerName;
        private int kills = 0;
        private long joinTime;

        public FFAParticipant(int playerId, String playerName) {
            this.playerId = playerId;
            this.playerName = playerName;
            this.joinTime = System.currentTimeMillis();
        }

        // Getters and setters
        public int getPlayerId() { return playerId; }
        public String getPlayerName() { return playerName; }
        public int getKills() { return kills; }
        public void incrementKills() { this.kills++; }
        public long getJoinTime() { return joinTime; }

        // Method for rejoining (preserves kills but updates join time)
        public void rejoin() {
            this.joinTime = System.currentTimeMillis();
        }
    }
}
