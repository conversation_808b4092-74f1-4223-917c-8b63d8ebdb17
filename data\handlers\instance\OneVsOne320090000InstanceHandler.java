package instance;

import com.aionemu.gameserver.instance.handlers.GeneralInstanceHandler;
import com.aionemu.gameserver.instance.handlers.InstanceID;
import com.aionemu.gameserver.model.gameobjects.Creature;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.OneVsOneService;
import com.aionemu.gameserver.services.player.PlayerReviveService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import com.aionemu.gameserver.world.WorldMapInstance;
import com.aionemu.gameserver.model.onevsone.OneVsOneMatch;

/**
 * OneVsOne Instance Handler for Custom 1v1 Map (320090000)
 * 
 * Manages the behavior of OneVsOne arena instances:
 * - Player death/resurrection handling
 * - Round-based combat system
 * - Proper revival mechanics
 * 
 * <AUTHOR> System
 */
@InstanceID(320090000)
public class OneVsOne320090000InstanceHandler extends GeneralInstanceHandler {

    public OneVsOne320090000InstanceHandler(WorldMapInstance instance) {
        super(instance);
    }

    @Override
    public void onInstanceCreate() {
        super.onInstanceCreate();
        // Instance created, ready for OneVsOne matches
    }

    @Override
    public void onEnterInstance(Player player) {
        super.onEnterInstance(player);
        // Player entered OneVsOne instance
    }

    @Override
    public void onLeaveInstance(Player player) {
        super.onLeaveInstance(player);
        // Clean up player from OneVsOne system when they leave
        // Only handle if player is actually in a match to prevent recursive calls
        if (OneVsOneService.getInstance().isPlayerInActiveMatch(player)) {
            OneVsOneService.getInstance().handlePlayerLeave(player);
        }
    }

    @Override
    public boolean onDie(Player player, Creature lastAttacker) {
        // Check if player is in an active OneVsOne match
        if (OneVsOneService.getInstance().isPlayerInActiveMatch(player)) {
            Integer matchInstanceId = OneVsOneService.getInstance().getPlayerMatchInstanceId(player.getObjectId());
            if (matchInstanceId != null) {
                OneVsOneMatch match = OneVsOneService.getInstance().getActiveMatch(matchInstanceId);
                if (match != null) {
                    // Always immediately resurrect players in OneVsOne matches to prevent resurrection dialog
                    // The OneVsOneService will handle the proper match logic and teleportation
                    PlayerReviveService.revive(player, 100, 100, false, 0);
                    player.getGameStats().updateStatsAndSpeedVisually();
                    player.unsetResPosState();
                    player.setPlayerResActivate(false);

                    // Handle player death in OneVsOne match after resurrection
                    if (lastAttacker instanceof Player killer) {
                        OneVsOneService.getInstance().onPlayerKill(killer, player);
                    }

                    return true; // Prevent default death handling and resurrection dialog
                }
            }
        }

        return super.onDie(player, lastAttacker);
    }

    @Override
    public boolean onReviveEvent(Player player) {
        // Handle resurrection in OneVsOne matches
        // Check if this is a round-based resurrection
        if (OneVsOneService.getInstance().isPlayerInActiveMatch(player)) {
            // Let OneVsOne service handle the resurrection
            return OneVsOneService.getInstance().handlePlayerRevive(player);
        }
        
        // Default resurrection for non-match situations
        PlayerReviveService.revive(player, 100, 100, true, 0);
        PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.STR_REBIRTH_MASSAGE_ME());
        player.getGameStats().updateStatsAndSpeedVisually();
        return true;
    }
}
