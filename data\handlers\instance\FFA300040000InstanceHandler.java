package instance;

import com.aionemu.gameserver.instance.handlers.InstanceID;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * FFA Instance Handler for Dark Poeta (300040000)
 * 
 * <AUTHOR> System
 */
@InstanceID(300040000)
public class FFA300040000InstanceHandler extends FFAInstanceHandler {

    public FFA300040000InstanceHandler(WorldMapInstance instance) {
        super(instance);
    }

    @Override
    protected void initializeSpawnLocations() {
        addSpawnLocation(1230.3514f, 409.39297f, 140.125f, (byte) 67);
        addSpawnLocation(1052.1534f, 311.02072f, 132.80185f, (byte) 41);
        addSpawnLocation(972.8223f, 530.3805f, 101.175385f, (byte) 78);
        addSpawnLocation(832.4362f, 577.91846f, 118.75f, (byte) 99);
        addSpawnLocation(641.57635f, 354.30344f, 103.125f, (byte) 19);
        addSpawnLocation(557.3073f, 490.47943f, 107.55174f, (byte) 2);
        addSpawnLocation(517.4669f, 671.3205f, 115.58252f, (byte) 8);
        addSpawnLocation(689.1145f, 861.0198f, 125.9925f, (byte) 62);
        addSpawnLocation(581.9522f, 971.206f, 128.01974f, (byte) 79);
        addSpawnLocation(433.6868f, 1056.9828f, 119.75f, (byte) 9); // Fixed coordinate (removed extra digit)
        addSpawnLocation(561.6822f, 1151.2819f, 139.39552f, (byte) 69);
        addSpawnLocation(334.62918f, 1175.837f, 151.73123f, (byte) 31);
        addSpawnLocation(234.94008f, 1302.3478f, 150.77277f, (byte) 114);
        addSpawnLocation(375.23392f, 1316.1445f, 156.88617f, (byte) 80);
        addSpawnLocation(361.7095f, 1247.0088f, 155.23184f, (byte) 97);
    }
}
